#!/usr/bin/env python3
"""
Demo data generator for the detection dashboard.
Creates sample detection entries and placeholder images for testing.
"""

import json
import os
import cv2
import numpy as np
from datetime import datetime, timedelta

# Configuration
DETECTIONS_DIR = "detections"
DETECTIONS_JSON = "detections.json"

def create_sample_image(width=640, height=480, text="Sample Detection"):
    """Create a sample detection image with text overlay."""
    # Create a dark background
    img = np.zeros((height, width, 3), dtype=np.uint8)
    img[:] = (40, 40, 40)  # Dark gray background
    
    # Add some noise for realism
    noise = np.random.randint(0, 50, (height, width, 3), dtype=np.uint8)
    img = cv2.add(img, noise)
    
    # Add a rectangle to simulate a detection box
    cv2.rectangle(img, (50, 50), (width-50, height-50), (0, 0, 255), 3)
    
    # Add text
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(img, text, (60, 100), font, 1, (255, 255, 255), 2)
    cv2.putText(img, "Demo Detection", (60, 140), font, 0.7, (0, 255, 0), 2)
    
    return img

def generate_demo_detections():
    """Generate sample detection data."""
    # Ensure directories exist
    os.makedirs(DETECTIONS_DIR, exist_ok=True)
    
    # Sample detection data
    detections = []
    base_time = datetime.now()
    
    sample_data = [
        {"type": "person", "confidence": 87.5, "minutes_ago": 5},
        {"type": "motorcycle", "confidence": 92.3, "minutes_ago": 15},
        {"type": "person", "confidence": 76.8, "minutes_ago": 25},
        {"type": "motorcycle", "confidence": 89.1, "minutes_ago": 35},
        {"type": "person", "confidence": 94.2, "minutes_ago": 45},
    ]
    
    for i, data in enumerate(sample_data):
        # Calculate timestamp
        detection_time = base_time - timedelta(minutes=data["minutes_ago"])
        timestamp_str = detection_time.strftime("%Y%m%d_%H%M%S")
        
        # Create filename
        filename = f"{timestamp_str}_{data['type']}_{int(data['confidence'])}.jpg"
        filepath = os.path.join(DETECTIONS_DIR, filename)
        
        # Create sample image
        img = create_sample_image(text=f"{data['type'].title()} {data['confidence']:.1f}%")
        cv2.imwrite(filepath, img)
        
        # Create detection record
        detection = {
            "timestamp": detection_time.isoformat(),
            "type": data["type"],
            "confidence": data["confidence"],
            "screenshot": filename,
            "id": i + 1
        }
        detections.append(detection)
        
        print(f"Created: {filename}")
    
    # Save detections to JSON
    with open(DETECTIONS_JSON, 'w') as f:
        json.dump(detections, f, indent=2)
    
    print(f"\nGenerated {len(detections)} demo detections")
    print(f"Screenshots saved to: {DETECTIONS_DIR}/")
    print(f"Detection data saved to: {DETECTIONS_JSON}")
    print("\nYou can now run the dashboard to see the demo data:")
    print("python test_dashboard.py")

if __name__ == "__main__":
    generate_demo_detections()
