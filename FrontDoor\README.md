# Detection Dashboard

A local web-based surveillance dashboard that replaces Telegram notifications with a modern, self-contained detection system.

## Features

- **Real-time Detection**: Monitors for persons and motorcycles using YOLO object detection
- **Web Dashboard**: Modern dark-themed interface accessible at http://localhost:8080
- **Screenshot Capture**: Automatically saves detection screenshots with bounding boxes
- **Auto-refresh**: Dashboard updates every 5 seconds without manual refresh
- **Offline Operation**: Completely self-contained, no external dependencies
- **Detection History**: Stores up to 100 recent detections with metadata

## File Structure

```
FrontDoor/
├── importcv2.py          # Main detection script with web server
├── dashboard.html        # Web dashboard interface
├── test_dashboard.py     # Test server for dashboard development
├── detections.json       # Detection records storage
├── detections/           # Directory for screenshot images
├── yolo11m.pt           # YOLO model file
└── README.md            # This file
```

## Requirements

- Python 3.7+
- OpenCV (`cv2`)
- Ultralytics YOLO (`ultralytics`)
- Flask (`flask`)

## Installation

1. Ensure all required Python packages are installed:
```bash
pip install opencv-python ultralytics flask
```

2. Update the `VIDEO_FILE_PATH` in `importcv2.py` to match your OBS output file.

## Usage

### Running the Detection System

1. Start the detection system:
```bash
cd FrontDoor
python importcv2.py
```

2. Open your web browser and navigate to:
```
http://localhost:8080
```

3. The dashboard will show:
   - System status and total detection count
   - Real-time detection entries with timestamps
   - Screenshot thumbnails with confidence scores
   - Detection type indicators (person/motorcycle)

### Testing the Dashboard

To test the web interface without running detection:

```bash
cd FrontDoor
python test_dashboard.py
```

This starts only the web server for development and testing.

## Configuration

Edit the following variables in `importcv2.py`:

- `VIDEO_FILE_PATH`: Path to your OBS recording file
- `OBJECTS_TO_DETECT`: List of objects to detect (default: ["person", "motorcycle"])
- `CONFIDENCE_THRESHOLD`: Minimum confidence for detections (default: 0.5)
- `DETECTION_COOLDOWN`: Seconds between detections (default: 10)
- `WEB_PORT`: Web server port (default: 8080)

## Dashboard Features

### Header Information
- **Status Indicator**: Green pulsing dot shows system is active
- **Total Detections**: Running count of all detections
- **System Status**: Current system state
- **Last Updated**: Timestamp of last dashboard refresh

### Detection Cards
Each detection shows:
- **Screenshot**: Captured frame with bounding boxes
- **Type Icon**: 🚶 for person, 🏍️ for motorcycle
- **Confidence**: Color-coded percentage (green ≥80%, yellow ≥60%, red <60%)
- **Timestamp**: When the detection occurred
- **Detection ID**: Unique identifier

### Auto-refresh
- Updates every 5 seconds automatically
- Refreshes when browser tab becomes active
- Shows loading states and error handling

## Data Storage

### Detection Records (`detections.json`)
```json
{
  "timestamp": "2024-01-15T14:30:45.123456",
  "type": "person",
  "confidence": 87.5,
  "screenshot": "20240115_143045_person_88.jpg",
  "id": 1
}
```

### Screenshots (`detections/`)
- Filename format: `YYYYMMDD_HHMMSS_type_confidence.jpg`
- Images include bounding boxes around detected objects
- Automatic cleanup keeps only recent detections

## Troubleshooting

### Web Dashboard Not Loading
1. Check if the script is running: `python importcv2.py`
2. Verify port 8080 is not in use by another application
3. Try accessing via `http://127.0.0.1:8080` instead

### No Detections Appearing
1. Verify `VIDEO_FILE_PATH` points to the correct OBS output file
2. Check that OBS is actively recording
3. Ensure objects are clearly visible in the video feed
4. Lower `CONFIDENCE_THRESHOLD` if needed

### Images Not Displaying
1. Check that the `detections/` directory exists and is writable
2. Verify screenshot files are being created
3. Check browser console for 404 errors

### Performance Issues
1. Increase `DETECTION_COOLDOWN` to reduce processing frequency
2. Use a smaller YOLO model (e.g., `yolo11n.pt` instead of `yolo11m.pt`)
3. Reduce video resolution in OBS settings

## Security Notes

- The web server runs on all interfaces (0.0.0.0) for local network access
- No authentication is implemented - suitable for local/private networks only
- Screenshots are stored locally and served directly by the web server

## Customization

### Adding New Object Types
1. Add object names to `OBJECTS_TO_DETECT` list
2. Update `getTypeIcon()` function in `dashboard.html` for custom icons

### Changing Theme Colors
Modify the Tailwind CSS configuration in `dashboard.html`:
```javascript
colors: {
    dark: {
        bg: '#0f172a',      // Background color
        card: '#1e293b',    // Card background
        border: '#334155',  // Border color
        text: '#f1f5f9'     // Text color
    }
}
```

### Adjusting Detection Limits
- Change the 100-detection limit in `save_detection()` function
- Modify auto-refresh interval in dashboard JavaScript (currently 5000ms)
