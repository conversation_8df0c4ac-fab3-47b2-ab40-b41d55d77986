import cv2
import time
from ultralytics import <PERSON><PERSON><PERSON>
import requests # Required for Telegram

# --- CONFIGURATION ---
VIDEO_FILE_PATH = "C:\Users\<USER>\Videos\Detections\2025-06-10 10-31-34.mp4" # IMPORTANT: Match this to your OBS output
MODEL = YOLO("yolo11m.pt")  # "n" is nano (fastest), can be "s", "m", "l", "x" for more accuracy
OBJECT_TO_DETECT = "motorcycle" # Change this to 'car', 'dog', etc. (based on COCO dataset names)
CONFIDENCE_THRESHOLD = 0.5 # Only detect objects with > 60% confidence
DETECTION_COOLDOWN = 10 # Seconds to wait before sending another alert

# --- TELEGRAM CONFIGURATION ---
TELEGRAM_TOKEN = "YOUR_TELEGRAM_BOT_TOKEN"
TELEGRAM_CHAT_ID = "YOUR_TELEGRAM_CHAT_ID"

last_alert_time = 0

def send_telegram_alert(frame):
    """Encodes an image and sends it via Telegram bot."""
    url = f"https://api.telegram.org/bot{TELEGRAM_TOKEN}/sendPhoto"
    # Encode image to a format that can be sent in a request
    success, encoded_image = cv2.imencode('.jpg', frame)
    if not success:
        print("Could not encode image for Telegram.")
        return

    # Prepare the file to be sent
    files = {'photo': ('detected_frame.jpg', encoded_image.tobytes(), 'image/jpeg')}
    payload = {'chat_id': TELEGRAM_CHAT_ID, 'caption': f"ALERT: {OBJECT_TO_DETECT.title()} detected!"}

    try:
        response = requests.post(url, files=files, data=payload)
        if response.status_code == 200:
            print("Telegram alert sent successfully.")
        else:
            print(f"Failed to send Telegram alert: {response.json()}")
    except Exception as e:
        print(f"Error sending Telegram alert: {e}")


# --- MAIN ANALYSIS LOOP ---
print(f"Starting analysis of: {VIDEO_FILE_PATH}")
print(f"Looking for object: '{OBJECT_TO_DETECT}'")

while True:
    cap = cv2.VideoCapture(VIDEO_FILE_PATH)
    if not cap.isOpened():
        print("Waiting for video file to be created by OBS...")
        time.sleep(5)
        continue

    # Fast-forward to the end of the video file to process the latest frames
    cap.set(cv2.CAP_PROP_POS_FRAMES, cap.get(cv2.CAP_PROP_FRAME_COUNT) - 1)

    while True:
        ret, frame = cap.read()
        if not ret:
            # No new frames, wait a moment and re-check the file
            time.sleep(0.5)
            break

        # Perform detection
        results = MODEL(frame, verbose=False) # verbose=False cleans up console output

        for result in results:
            for box in result.boxes:
                class_id = int(box.cls[0])
                label = result.names[class_id]
                confidence = float(box.conf[0])

                if label == OBJECT_TO_DETECT and confidence > CONFIDENCE_THRESHOLD:
                    current_time = time.time()
                    if (current_time - last_alert_time) > DETECTION_COOLDOWN:
                        print(f"OBJECT DETECTED: {label} with {confidence:.2f} confidence.")

                        # Draw a box on the frame for the alert image
                        x1, y1, x2, y2 = [int(i) for i in box.xyxy[0]]
                        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 0, 255), 2)
                        cv2.putText(frame, f"{label} {confidence:.2f}", (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 0, 255), 2)

                        send_telegram_alert(frame)
                        last_alert_time = current_time
                    break # Move to next frame once an alert is sent

    cap.release()