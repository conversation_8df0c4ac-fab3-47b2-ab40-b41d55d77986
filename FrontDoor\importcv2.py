import cv2
import time
import os
import json
import threading
from datetime import datetime
from ultralytics import YOL<PERSON>
from flask import Flask, jsonify, send_from_directory, render_template_string

# --- CONFIGURATION ---
VIDEO_FILE_PATH = r"C:\Users\<USER>\Videos\Detections\2025-06-10 11-06-31.mp4" # IMPORTANT: Match this to your OBS output
MODEL = YOLO("yolo11m.pt")  # "n" is nano (fastest), can be "s", "m", "l", "x" for more accuracy
OBJECTS_TO_DETECT = ["person", "motorcycle"] # Detect both person and motorcycle
CONFIDENCE_THRESHOLD = 0.5 # Only detect objects with > 50% confidence
DETECTION_COOLDOWN = 10 # Seconds to wait before saving another detection
WEB_PORT = 8080 # Port for the web dashboard

# --- PATHS ---
DETECTIONS_DIR = "detections"
DETECTIONS_JSON = "detections.json"

# Ensure directories exist
os.makedirs(DETECTIONS_DIR, exist_ok=True)

last_alert_time = 0
app = Flask(__name__)

def load_detections():
    """Load existing detections from JSON file."""
    if os.path.exists(DETECTIONS_JSON):
        try:
            with open(DETECTIONS_JSON, 'r') as f:
                return json.load(f)
        except:
            return []
    return []

def save_detection(detection_type, confidence, screenshot_path):
    """Save a new detection to the JSON file."""
    detections = load_detections()

    new_detection = {
        "timestamp": datetime.now().isoformat(),
        "type": detection_type,
        "confidence": round(confidence * 100, 1),  # Convert to percentage
        "screenshot": screenshot_path,
        "id": len(detections) + 1
    }

    detections.append(new_detection)

    # Keep only the last 100 detections to prevent file from growing too large
    if len(detections) > 100:
        detections = detections[-100:]

    try:
        with open(DETECTIONS_JSON, 'w') as f:
            json.dump(detections, f, indent=2)
        print(f"Detection saved: {detection_type} ({new_detection['confidence']}%)")
    except Exception as e:
        print(f"Error saving detection: {e}")

def save_screenshot(frame, detection_type, confidence):
    """Save screenshot with bounding boxes and return filename."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{timestamp}_{detection_type}_{confidence:.0f}.jpg"
    filepath = os.path.join(DETECTIONS_DIR, filename)

    try:
        cv2.imwrite(filepath, frame)
        return filename
    except Exception as e:
        print(f"Error saving screenshot: {e}")
        return None

# --- FLASK WEB SERVER ---
@app.route('/')
def dashboard():
    """Serve the main dashboard page."""
    return send_from_directory('.', 'dashboard.html')

@app.route('/api/detections')
def api_detections():
    """API endpoint to get all detections."""
    detections = load_detections()
    return jsonify({
        "detections": detections,
        "total_count": len(detections),
        "status": "active"
    })

@app.route('/detections/<filename>')
def serve_screenshot(filename):
    """Serve screenshot images."""
    return send_from_directory(DETECTIONS_DIR, filename)

def start_web_server():
    """Start the Flask web server in a separate thread."""
    app.run(host='0.0.0.0', port=WEB_PORT, debug=False, use_reloader=False)

# --- MAIN ANALYSIS LOOP ---
def run_detection():
    """Main detection loop."""
    global last_alert_time

    print(f"Starting analysis of: {VIDEO_FILE_PATH}")
    print(f"Looking for objects: {', '.join(OBJECTS_TO_DETECT)}")
    print(f"Web dashboard will be available at: http://localhost:{WEB_PORT}")

    while True:
        cap = cv2.VideoCapture(VIDEO_FILE_PATH)
        if not cap.isOpened():
            print("Waiting for video file to be created by OBS...")
            time.sleep(5)
            continue

        # Fast-forward to the end of the video file to process the latest frames
        cap.set(cv2.CAP_PROP_POS_FRAMES, cap.get(cv2.CAP_PROP_FRAME_COUNT) - 1)

        while True:
            ret, frame = cap.read()
            if not ret:
                # No new frames, wait a moment and re-check the file
                time.sleep(0.5)
                break

            # Perform detection
            results = MODEL(frame, verbose=False) # verbose=False cleans up console output

            for result in results:
                for box in result.boxes:
                    class_id = int(box.cls[0])
                    label = result.names[class_id]
                    confidence = float(box.conf[0])

                    if label in OBJECTS_TO_DETECT and confidence > CONFIDENCE_THRESHOLD:
                        current_time = time.time()
                        if (current_time - last_alert_time) > DETECTION_COOLDOWN:
                            print(f"OBJECT DETECTED: {label} with {confidence:.2f} confidence.")

                            # Draw a box on the frame for the screenshot
                            x1, y1, x2, y2 = [int(i) for i in box.xyxy[0]]
                            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 0, 255), 2)
                            cv2.putText(frame, f"{label} {confidence:.2f}", (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 0, 255), 2)

                            # Save screenshot and detection data
                            screenshot_filename = save_screenshot(frame, label, confidence * 100)
                            if screenshot_filename:
                                save_detection(label, confidence, screenshot_filename)

                            last_alert_time = current_time
                        break # Move to next frame once a detection is processed

        cap.release()

if __name__ == "__main__":
    # Start web server in a separate thread
    web_thread = threading.Thread(target=start_web_server, daemon=True)
    web_thread.start()

    # Give the web server a moment to start
    time.sleep(2)

    # Start the detection loop
    try:
        run_detection()
    except KeyboardInterrupt:
        print("\nShutting down detection system...")
    except Exception as e:
        print(f"Error in detection system: {e}")