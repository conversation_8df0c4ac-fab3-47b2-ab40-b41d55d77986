#!/usr/bin/env python3
"""
Test script for the detection dashboard.
This script starts only the web server without the detection loop for testing.
"""

import os
import json
import threading
import time
from flask import Flask, jsonify, send_from_directory

# Configuration
WEB_PORT = 8080
DETECTIONS_DIR = "detections"
DETECTIONS_JSON = "detections.json"

app = Flask(__name__)

def load_detections():
    """Load existing detections from JSON file."""
    if os.path.exists(DETECTIONS_JSON):
        try:
            with open(DETECTIONS_JSON, 'r') as f:
                return json.load(f)
        except:
            return []
    return []

@app.route('/')
def dashboard():
    """Serve the main dashboard page."""
    return send_from_directory('.', 'dashboard.html')

@app.route('/api/detections')
def api_detections():
    """API endpoint to get all detections."""
    detections = load_detections()
    return jsonify({
        "detections": detections,
        "total_count": len(detections),
        "status": "testing"
    })

@app.route('/detections/<filename>')
def serve_screenshot(filename):
    """Serve screenshot images."""
    return send_from_directory(DETECTIONS_DIR, filename)

if __name__ == "__main__":
    print(f"Starting test web server on http://localhost:{WEB_PORT}")
    print("Press Ctrl+C to stop")
    
    try:
        app.run(host='0.0.0.0', port=WEB_PORT, debug=True)
    except KeyboardInterrupt:
        print("\nShutting down test server...")
