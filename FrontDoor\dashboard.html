<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detection Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        dark: {
                            bg: '#0f172a',
                            card: '#1e293b',
                            border: '#334155',
                            text: '#f1f5f9'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .detection-card {
            transition: all 0.3s ease;
        }
        .detection-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }
        .status-indicator {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body class="dark bg-dark-bg text-dark-text min-h-screen">
    <!-- Header -->
    <header class="bg-dark-card border-b border-dark-border">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-green-500 rounded-full status-indicator"></div>
                        <h1 class="text-2xl font-bold text-white">Detection Dashboard</h1>
                    </div>
                </div>
                <div class="flex items-center space-x-6">
                    <div class="text-sm">
                        <span class="text-gray-400">Total Detections:</span>
                        <span id="total-count" class="text-white font-semibold ml-1">0</span>
                    </div>
                    <div class="text-sm">
                        <span class="text-gray-400">Status:</span>
                        <span id="system-status" class="text-green-400 font-semibold ml-1">Active</span>
                    </div>
                    <div class="text-sm">
                        <span class="text-gray-400">Last Updated:</span>
                        <span id="last-updated" class="text-white font-semibold ml-1">--</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Loading State -->
        <div id="loading" class="text-center py-12">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
            <p class="mt-4 text-gray-400">Loading detections...</p>
        </div>

        <!-- No Detections State -->
        <div id="no-detections" class="text-center py-12 hidden">
            <div class="text-6xl mb-4">👁️</div>
            <h3 class="text-xl font-semibold text-white mb-2">No Detections Yet</h3>
            <p class="text-gray-400">The system is monitoring for persons and motorcycles.</p>
        </div>

        <!-- Detections Grid -->
        <div id="detections-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 hidden">
        </div>
    </main>

    <script>
        let lastUpdateTime = null;
        
        function formatTimestamp(isoString) {
            const date = new Date(isoString);
            return date.toLocaleString();
        }
        
        function getConfidenceColor(confidence) {
            if (confidence >= 80) return 'bg-green-500';
            if (confidence >= 60) return 'bg-yellow-500';
            return 'bg-red-500';
        }
        
        function getTypeIcon(type) {
            return type === 'person' ? '🚶' : '🏍️';
        }
        
        function createDetectionCard(detection) {
            return `
                <div class="detection-card bg-dark-card rounded-lg border border-dark-border overflow-hidden">
                    <div class="aspect-video bg-gray-800 relative">
                        <img 
                            src="/detections/${detection.screenshot}" 
                            alt="Detection screenshot"
                            class="w-full h-full object-cover"
                            onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzc0MTUxIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk0YTNiOCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBmb3VuZDwvdGV4dD48L3N2Zz4='"
                        >
                        <div class="absolute top-2 left-2">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-dark-bg text-white">
                                ${getTypeIcon(detection.type)} ${detection.type}
                            </span>
                        </div>
                        <div class="absolute top-2 right-2">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-white ${getConfidenceColor(detection.confidence)}">
                                ${detection.confidence}%
                            </span>
                        </div>
                    </div>
                    <div class="p-4">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-400">
                                ${formatTimestamp(detection.timestamp)}
                            </div>
                            <div class="text-xs text-gray-500">
                                ID: ${detection.id}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        async function fetchDetections() {
            try {
                const response = await fetch('/api/detections');
                const data = await response.json();
                
                // Update header stats
                document.getElementById('total-count').textContent = data.total_count;
                document.getElementById('system-status').textContent = data.status;
                document.getElementById('last-updated').textContent = new Date().toLocaleTimeString();
                
                // Update UI based on detections
                const loading = document.getElementById('loading');
                const noDetections = document.getElementById('no-detections');
                const grid = document.getElementById('detections-grid');
                
                loading.classList.add('hidden');
                
                if (data.detections.length === 0) {
                    noDetections.classList.remove('hidden');
                    grid.classList.add('hidden');
                } else {
                    noDetections.classList.add('hidden');
                    grid.classList.remove('hidden');
                    
                    // Sort detections by timestamp (newest first)
                    const sortedDetections = data.detections.sort((a, b) => 
                        new Date(b.timestamp) - new Date(a.timestamp)
                    );
                    
                    // Render detection cards
                    grid.innerHTML = sortedDetections.map(createDetectionCard).join('');
                }
                
                lastUpdateTime = new Date();
                
            } catch (error) {
                console.error('Error fetching detections:', error);
                document.getElementById('system-status').textContent = 'Error';
                document.getElementById('system-status').className = 'text-red-400 font-semibold ml-1';
            }
        }
        
        // Initial load
        fetchDetections();
        
        // Auto-refresh every 5 seconds
        setInterval(fetchDetections, 5000);
        
        // Refresh when page becomes visible again
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                fetchDetections();
            }
        });
    </script>
</body>
</html>
